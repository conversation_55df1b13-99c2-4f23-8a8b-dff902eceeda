package models

import "time"

type CheckinType string

const (
	CheckinTypeOfficeAKV CheckinType = "OFFICE_AKV"
	CheckinTypeOfficeHQ    CheckinType = "OFFICE_HQ"
	CheckinTypeWfh    CheckinType = "WFH"
	CheckinTypeOnsite CheckinType = "ONSITE"
	CheckinTypeBusinessTrip CheckinType = "BUSINESS_TRIP"
	CheckinTypeBusiness CheckinType = "BUSINESS_LEAVE"
	CheckinTypeAnnual  CheckinType = "ANNUAL_LEAVE"
	CheckinTypeSick  CheckinType = "SICK_LEAVE"
	CheckinTypeMenstrual  CheckinType = "MENSTRUAL_LEAVE"
	CheckinTypeBirthday  CheckinType = "BIRTHDAY_LEAVE"
	CheckinTypeOrdination  CheckinType = "ORDINATION_LEAVE"
)


type LeavePeriod string

const (
	LeavePeriodHalfMorning   LeavePeriod = "HALF_MORNING"
	LeavePeriodHalfAfternoon LeavePeriod = "HALF_AFTERNOON"
	LeavePeriodFullDay       LeavePeriod = "FULL_DAY"
	LeavePeriodManyDays      LeavePeriod = "MANY_DAYS"
)

type Checkin struct {
	BaseModelHardDelete
	UserId      string      `json:"user_id" gorm:"column:user_id;type:uuid"`
	Type        CheckinType `json:"type" gorm:"column:type"`
	LeavePeriod LeavePeriod `json:"leave_period" gorm:"column:leave_period"`
	Location    *string     `json:"location" gorm:"column:location"`
	Remarks     *string     `json:"remarks" gorm:"column:remarks"`
	IsUnused    *bool       `json:"is_unused" gorm:"column:is_unused;default:false"`
	Date        *time.Time  `json:"date" gorm:"column:date;type:date"`
	// Relations
	User User `json:"user" gorm:"foreignKey:UserId;references:ID;constraint:OnDelete:CASCADE"`
}

func (Checkin) TableName() string {
	return "checkins"
}

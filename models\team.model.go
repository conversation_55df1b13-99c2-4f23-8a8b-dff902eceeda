package models

type Team struct {
	BaseModelHardDelete
	Name           string  `json:"name" gorm:"column:name"`
	Code           string  `json:"code" gorm:"column:code"`
	Color          string  `json:"color" gorm:"column:color"`
	Description    *string `json:"description" gorm:"column:description"`
	StartWorkingAt *string `json:"start_working_at" gorm:"column:start_working_at;type:time"`
	EndWorkingAt   *string `json:"end_working_at" gorm:"column:end_working_at;type:time"`

	// Relations
	Users []User `json:"users" gorm:"foreignKey:TeamCode;references:Code"`
}

func (Team) TableName() string {
	return "teams"
}

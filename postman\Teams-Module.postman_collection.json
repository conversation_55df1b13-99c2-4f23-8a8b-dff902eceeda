{"info": {"_postman_id": "teams-module-collection", "name": "Finework API - Teams Module", "description": "Team management endpoints for Finework API with full CRUD operations", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Teams", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/teams?page={{page}}&limit={{limit}}", "host": ["{{baseUrl}}"], "path": ["teams"], "query": [{"key": "page", "value": "{{page}}", "description": "Page number for pagination"}, {"key": "limit", "value": "{{limit}}", "description": "Number of items per page"}]}, "description": "Retrieve paginated list of all teams"}, "response": []}, {"name": "Get Team by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/teams/{{team_id}}", "host": ["{{baseUrl}}"], "path": ["teams", "{{team_id}}"]}, "description": "Retrieve a specific team by their ID"}, "response": []}, {"name": "Get Team by Code", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/teams/code/{{team_code}}", "host": ["{{baseUrl}}"], "path": ["teams", "code", "{{team_code}}"]}, "description": "Retrieve a specific team by their code"}, "response": []}, {"name": "Create Team", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Development Team\",\n  \"code\": \"DEV\",\n  \"description\": \"Software development team\",\n  \"start_working_at\": \"2024-01-01T09:00:00Z\",\n  \"end_working_at\": \"2024-01-01T18:00:00Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/teams", "host": ["{{baseUrl}}"], "path": ["teams"]}, "description": "Create a new team. Name and code must be unique."}, "response": []}, {"name": "Update Team", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Development Team Updated\",\n  \"code\": \"DEV\",\n  \"description\": \"Updated software development team\",\n  \"start_working_at\": \"2024-01-01T08:30:00Z\",\n  \"end_working_at\": \"2024-01-01T17:30:00Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/teams/{{team_id}}", "host": ["{{baseUrl}}"], "path": ["teams", "{{team_id}}"]}, "description": "Update an existing team. All fields are optional for updates."}, "response": []}, {"name": "Delete Team", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/teams/{{team_id}}", "host": ["{{baseUrl}}"], "path": ["teams", "{{team_id}}"]}, "description": "Delete a team by their ID"}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "token", "value": "YOUR_AUTH_TOKEN_HERE", "type": "string"}, {"key": "page", "value": "1", "type": "string"}, {"key": "limit", "value": "10", "type": "string"}, {"key": "team_id", "value": "TEAM_ID_HERE", "type": "string"}, {"key": "team_code", "value": "DEV", "type": "string"}]}
model teams {
  id               String    @id @default(uuid()) @db.Uuid
  name             String    @unique
  code             String    @unique
  description      String?
  color            String    @default("")
  start_working_at DateTime? @db.Time()
  end_working_at   DateTime? @db.Time()

  created_at DateTime @default(now())
  updated_at DateTime @default(now()) @updatedAt

  // Relations
  users users[]

  @@index([name, code])
}

enum LeaveType {
  AN<PERSON>AL
  SICK
  BUSINESS
  MENSTRUAL
  BIRTHDAY
  ORDINATION
}

enum TimesheetType {
  PROJECT
  SGA
  LEAVE
  INTERNAL
  EXTERNAL
  OT
}

model timesheets {
  id           String        @id @default(uuid()) @db.Uuid
  user_id      String        @db.Uuid
  sga_id       String?       @db.Uuid
  project_code String?
  timing       Float
  type         TimesheetType
  leave_type   LeaveType?
  description  String?
  date         DateTime      @db.Date()
  created_at   DateTime      @default(now())
  updated_at   DateTime      @default(now()) @updatedAt

  // Relations
  project projects? @relation(fields: [project_code], references: [code], onDelete: SetNull)
  user    users     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  sga     sgas?     @relation(fields: [sga_id], references: [id], onDelete: SetNull)

  @@index([type])
}

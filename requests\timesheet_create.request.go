package requests

import (
	"strings"
	"time"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type TimesheetCreate struct {
	core.BaseValidator
	ProjectCode *string  `json:"project_code"`
	SgaID       *string  `json:"sga_id"`
	Timing      *float64 `json:"timing"`
	Type        *string  `json:"type"`
	LeaveType   *string  `json:"leave_type"`
	Description *string  `json:"description"`
	Date        *string  `json:"date"`
}

func (r *TimesheetCreate) Valid(ctx core.IContext) core.IError {
	if r.Must(r.IsRequired(r.Type, "type")) {
		switch utils.ToNonPointer(r.Type) {
		case string(models.TimesheetTypeLeave):
			r.ProjectCode = nil
			r.SgaID = nil
			r.Must(r.<PERSON>equired(r.LeaveType, "leave_type"))
			r.Must(r.Is<PERSON>trIn(r.LeaveType, strings.Join([]string{string(models.LeaveTypeAnnual), string(models.LeaveTypeSick),
				string(models.LeaveTypeBusiness), string(models.LeaveTypeMenstrual),
				string(models.LeaveTypeBirthday), string(models.LeaveTypeOrdination)}, "|"), "leave_type"))
		case string(models.TimesheetTypeProject):
			r.SgaID = nil
			r.LeaveType = nil
			r.Must(r.IsStrRequired(r.ProjectCode, "project_code"))
		case string(models.TimesheetTypeSga):
			r.ProjectCode = nil
			r.LeaveType = nil
			r.Must(r.IsStrRequired(r.SgaID, "sga_id"))
		}
	}

	if r.Must(r.IsRequired(r.Date, "date")) {
		r.Must(r.IsDate(r.Date, "date"))
		if _, err := time.Parse("2006-01-02", *r.Date); err != nil {
			r.Must(false, &core.IValidMessage{
				Name:    "date",
				Code:    "INVALID_DATE_FORMAT",
				Message: "Date must be in YYYY-MM-DD format",
			})
		}
	}

	r.Must(r.IsRequired(r.Timing, "timing"))
	r.Must(r.IsStrIn(r.Type, strings.Join([]string{string(models.TimesheetTypeProject), string(models.TimesheetTypeSga),
		string(models.TimesheetTypeLeave), string(models.TimesheetTypeInternal),
		string(models.TimesheetTypeExternal), string(models.TimesheetTypeOt)}, "|"), "type"))

	return r.Error()
}

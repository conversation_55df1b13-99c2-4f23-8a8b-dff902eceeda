package services

import "time"

type CheckinCreatePayload struct {
	UserId      string
	Type        string
	LeavePeriod string
	Location    *string
	Remarks     *string
	IsUnused    *bool
	Date        *time.Time
}

type CheckinUpdatePayload struct {
	UserId      string
	Type        string
	LeavePeriod string
	Location    *string
	Remarks     *string
	IsUnused    *bool
	Date        *time.Time
}

type CheckinPaginationOptions struct {
	UserID    *string `json:"user_id"`
	StartDate *string `json:"start_date"`
	EndDate   *string `json:"end_date"`
}

package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type ICheckinService interface {
	Create(input *CheckinCreatePayload, userID string) (*models.Checkin, core.IError)
	Update(id string, input *CheckinUpdatePayload) (*models.Checkin, core.IError)
	Find(id string) (*models.Checkin, core.IError)
	Pagination(pageOptions *core.PageOptions, options *CheckinPaginationOptions) (*repository.Pagination[models.Checkin], core.IError)
	Delete(id string) core.IError
}

type checkinService struct {
	ctx core.IContext
}

func (s checkinService) Create(input *CheckinCreatePayload, userID string) (*models.Checkin, core.IError) {
	checkin := &models.Checkin{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		UserId:              userID,
		Type:                models.CheckinType(input.Type),
		LeavePeriod:         models.LeavePeriod(input.LeavePeriod),
		Location:            input.Location,
		Remarks:             input.Remarks,
		IsUnused:            input.IsUnused,
		Date:                input.Date,
	}

	ierr := repo.Checkin(s.ctx).Create(checkin)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(checkin.ID)
}

func (s checkinService) Update(id string, input *CheckinUpdatePayload) (*models.Checkin, core.IError) {
	checkin, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	if input.Date != nil {
		checkin.Date = input.Date
	}
	if input.LeavePeriod != "" {
		checkin.LeavePeriod = models.LeavePeriod(input.LeavePeriod)
	}
	if input.Location != nil {
		checkin.Location = input.Location
	}
	if input.Remarks != nil {
		checkin.Remarks = input.Remarks
	}
	if input.IsUnused != nil {
		checkin.IsUnused = input.IsUnused
	}
	if input.Type != "" {
		checkin.Type = models.CheckinType(input.Type)
	}

	// Update the checkin in the repository
	ierr = repo.Checkin(s.ctx).Where("id = ?", id).Updates(checkin)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(checkin.ID)
}

func (s checkinService) Find(id string) (*models.Checkin, core.IError) {
	return repo.Checkin(s.ctx, repo.CheckinWithAllRelation()).FindOne("id = ?", id)
}

func (s checkinService) Pagination(pageOptions *core.PageOptions, options *CheckinPaginationOptions) (*repository.Pagination[models.Checkin], core.IError) {
	return repo.Checkin(
		s.ctx,
		repo.CheckinWithAllRelation(),
		repo.CheckinWithUser(options.UserID),
		repo.CheckinWithDateRange(options.StartDate, options.EndDate),
		repo.CheckinOrderBy(pageOptions)).
		Pagination(pageOptions)
}

func (s checkinService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.Checkin(s.ctx).Delete("id = ?", id)
}

func NewCheckinService(ctx core.IContext) ICheckinService {
	return &checkinService{ctx: ctx}
}

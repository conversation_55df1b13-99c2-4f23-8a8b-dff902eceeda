package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ITeamService interface {
	Create(input *TeamCreatePayload) (*models.Team, core.IError)
	Update(id string, input *TeamUpdatePayload) (*models.Team, core.IError)
	Find(id string) (*models.Team, core.IError)
	FindByCode(code string) (*models.Team, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Team], core.IError)
	Delete(id string) core.IError
}

type teamService struct {
	ctx core.IContext
}

func (s teamService) Create(input *TeamCreatePayload) (*models.Team, core.IError) {
	team := &models.Team{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		Name:                input.Name,
		Code:                input.Code,
		Description:         utils.ToPointer(input.Description),
		Color:               input.Color,
		StartWorkingAt:      input.StartWorkingAt,
		EndWorkingAt:        input.EndWorkingAt,
	}

	ierr := repo.Team(s.ctx).Create(team)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(team.ID)
}

func (s teamService) Update(id string, input *TeamUpdatePayload) (*models.Team, core.IError) {
	team, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.Name != "" {
		team.Name = input.Name
	}
	if input.Code != "" {
		team.Code = input.Code
	}
	if input.Description != "" {
		team.Description = utils.ToPointer(input.Description)
	}
	if input.Color != "" {
		team.Color = input.Color
	}

	if input.StartWorkingAt != nil {
		team.StartWorkingAt = input.StartWorkingAt
	}
	if input.EndWorkingAt != nil {
		team.EndWorkingAt = input.EndWorkingAt
	}

	ierr = repo.Team(s.ctx).Where("id = ?", id).Updates(team)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(team.ID)
}

func (s teamService) Find(id string) (*models.Team, core.IError) {
	return repo.Team(s.ctx).FindOne("id = ?", id)
}

func (s teamService) FindByCode(code string) (*models.Team, core.IError) {
	return repo.Team(s.ctx).FindOne("code = ?", code)
}

func (s teamService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Team], core.IError) {
	return repo.Team(s.ctx, repo.TeamOrderBy(pageOptions)).Pagination(pageOptions)
}

func (s teamService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.Team(s.ctx).Delete("id = ?", id)
}

func NewTeamService(ctx core.IContext) ITeamService {
	return &teamService{ctx: ctx}
}
